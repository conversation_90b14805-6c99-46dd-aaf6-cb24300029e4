/**
 * Report API E2E 测试
 * 测试 /report/api/csv/v1 和 /report/api/file/:name 接口
 */

import { Server } from 'http';
import supertest from 'supertest';
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { generateReportTestData, invalidAuth } from './setup/test-data';
import {
  cleanupTestEnvironment,
  createAuthenticatedRequest,
  createUnauthenticatedRequest,
  expectAuthFailureResponse,
  expectDownloadUrlResponse,
  expectParamsInvalidResponse,
  generateRandomString
} from './setup/test-helpers';
import { createTestApp } from './setup/test-server';

describe('Report API E2E Tests', () => {
  let server: Server;
  let request: supertest.Agent;

  beforeAll(async () => {
    const app = createTestApp();
    server = app.listen();
    request = supertest(server);
  });

  afterAll(async () => {
    await cleanupTestEnvironment(server);
  });

  describe('/report/api/csv/v1', () => {
    const reportTestData = generateReportTestData();

    describe('POST requests', () => {
      it('应该成功处理单个日期的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.valid.singleDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        expectDownloadUrlResponse(response);
      });

      it('应该成功处理多个日期的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.valid.multipleDates,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        expectDownloadUrlResponse(response);
      });

      it('应该拒绝缺少date参数的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.invalid.missingDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝空日期数组的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.invalid.emptyDateArray,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.invalid.invalidDateFormat,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝未来日期的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.invalid.futureDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('认证和权限测试', () => {
      it('应该拒绝缺少认证头的请求', async () => {
        const response = await createUnauthenticatedRequest(request, {
          method: 'POST',
          url: '/report/api/csv/v1',
          body: reportTestData.valid.singleDate,
          headers: { 'Content-Type': 'application/json' }
        });

        expectAuthFailureResponse(response);
      });

      it('应该拒绝无效authorization类型的请求', async () => {
        const response = await request
          .post('/report/api/csv/v1')
          .set('x-authorization', invalidAuth.wrongTypeAuth.toString())
          .set('Content-Type', 'application/json')
          .send(reportTestData.valid.singleDate);

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝空authorization的请求', async () => {
        const response = await request
          .post('/report/api/csv/v1')
          .set('x-authorization', '')
          .set('Content-Type', 'application/json')
          .send(reportTestData.valid.singleDate);

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效authorization的请求', async () => {
        const response = await request
          .post('/report/api/csv/v1')
          .set('x-authorization', 'invalid_authorization_token')
          .set('Content-Type', 'application/json')
          .send(reportTestData.valid.singleDate);

        expectAuthFailureResponse(response);
      });
    });

    describe('业务规则测试', () => {
      it('应该验证用户权限（仅超管和管理员可访问）', async () => {
        // 这个测试需要模拟不同权限的用户
        // 由于我们使用的是固定的测试token，这里主要测试权限验证逻辑
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.valid.singleDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        // 根据测试token的权限，这里可能成功或失败
        // 如果成功，验证响应格式；如果失败，验证是权限错误
        if (response.status === 200) {
          expectDownloadUrlResponse(response);
        } else {
          expectAuthFailureResponse(response);
        }
      });

      it('应该处理租户ID验证', async () => {
        // 测试租户ID验证逻辑
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/report/api/csv/v1',
            body: reportTestData.valid.singleDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'report'
        );

        // 根据host_prefix的设置，可能会有不同的结果
        expect([200, 403]).toContain(response.status);
      });
    });
  });

  describe('/report/api/file/:name', () => {
    describe('GET requests', () => {
      it('应该拒绝无效文件名的请求', async () => {
        const response = await request.get(
          '/report/api/file/invalid_file_name.csv'
        );

        expect(response.status).toBe(403);
      });

      it('应该拒绝缺少文件名的请求', async () => {
        const response = await request.get('/report/api/file/');

        expect(response.status).toBe(404);
      });

      it('应该拒绝非CSV文件的请求', async () => {
        const response = await request.get('/report/api/file/test.txt');

        expect(response.text).toBe('Not Found');
      });

      it('应该拒绝无效加密token的请求', async () => {
        const invalidToken = generateRandomString(32);
        const response = await request.get(
          `/report/api/file/${invalidToken}.csv`
        );

        expect(response.status).toBe(403);
      });
    });

    describe('文件下载测试', () => {
      it('应该正确处理文件名格式验证', async () => {
        // 测试各种文件名格式
        const testCases = [
          'test.csv', // 正常格式
          'test.CSV', // 大写扩展名
          'test.pdf', // 错误扩展名
          'test', // 无扩展名
          '.csv', // 无文件名
          'test.csv.backup' // 多重扩展名
        ];

        for (const fileName of testCases) {
          const response = await request.get(`/report/api/file/${fileName}`);

          if (
            fileName.toLowerCase().endsWith('.csv') &&
            fileName.split('.')[0]
          ) {
            // CSV文件且有文件名，应该进行token验证（通常会失败因为token无效）
            expect([403, 200]).toContain(response.status);
          } else {
            // 非CSV文件或格式错误，应该返回Not Found
            expect(response.text).toBe('Not Found');
          }
        }
      });

      it('应该正确设置下载响应头', async () => {
        // 这个测试需要有效的文件token，在实际环境中可能无法完全测试
        // 但我们可以测试响应头的设置逻辑
        const testToken = generateRandomString(32);
        const response = await request.get(`/report/api/file/${testToken}.csv`);

        // 由于token无效，通常会返回403或Not Found
        // 但我们可以验证错误处理是正确的
        expect([403, 200]).toContain(response.status);
      });
    });

    describe('安全性测试', () => {
      it('应该防止路径遍历攻击', async () => {
        const maliciousNames = [
          '../../../etc/passwd.csv',
          '..\\..\\windows\\system32\\config\\sam.csv',
          '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd.csv'
        ];

        for (const name of maliciousNames) {
          const response = await request.get(`/report/api/file/${name}`);

          // 应该被安全地处理，不会访问到系统文件
          expect([403, 404]).toContain(response.status);
        }
      });

      it('应该验证文件token的完整性', async () => {
        // 测试各种无效token格式
        const invalidTokens = [
          '', // 空token
          'a', // 太短的token
          'a'.repeat(1000), // 太长的token
          'invalid-chars-!@#$.csv', // 包含特殊字符
          'normal_token.exe' // 错误扩展名
        ];

        for (const token of invalidTokens) {
          const response = await request.get(`/report/api/file/${token}`);

          if (token.toLowerCase().endsWith('.csv') && token.split('.')[0]) {
            expect([403, 200]).toContain(response.status);
          } else {
            expect(response.text).toBe('Not Found');
          }
        }
      });
    });
  });
});
