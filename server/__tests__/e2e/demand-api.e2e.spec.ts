/**
 * Demand API E2E 测试
 * 测试 /demand/api/v1 和 /demand/api/v2 接口
 */

import { Server } from 'http';
import supertest from 'supertest';
import { afterAll, beforeAll, describe, it } from 'vitest';
import {
  generateV1TestData,
  generateV2TestData,
  invalidAuth,
  validAuth,
  validTimezones
} from './setup/test-data';
import {
  cleanupTestEnvironment,
  createAuthenticatedRequest,
  createUnauthenticatedRequest,
  expectAuthFailureResponse,
  expectParamsInvalidResponse,
  expectReportDataResponse
} from './setup/test-helpers';
import { createTestApp } from './setup/test-server';

describe('Demand API E2E Tests', () => {
  let server: Server;
  let request: supertest.Agent;

  beforeAll(async () => {
    const app = createTestApp();
    server = app.listen();
    request = supertest(server);
  });

  afterAll(async () => {
    await cleanupTestEnvironment(server);
  });

  describe('/demand/api/v1', () => {
    const v1TestData = generateV1TestData();

    describe('POST requests', () => {
      it('应该成功处理有效的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v1',
            body: v1TestData.valid,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少day参数的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v1',
            body: v1TestData.invalid.missingDay,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v1',
            body: v1TestData.invalid.invalidDay,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝未来日期的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v1',
            body: v1TestData.invalid.futureDay,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('GET requests', () => {
      it('应该成功处理有效的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/demand/api/v1',
            query: v1TestData.valid
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少day参数的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/demand/api/v1',
            query: v1TestData.invalid.missingDay
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('认证测试', () => {
      it('应该拒绝缺少认证头的请求', async () => {
        const response = await createUnauthenticatedRequest(request, {
          method: 'POST',
          url: '/demand/api/v1',
          body: v1TestData.valid,
          headers: { 'Content-Type': 'application/json' }
        });

        expectAuthFailureResponse(response);
      });

      it('应该拒绝无效userid类型的请求', async () => {
        const response = await request
          .post('/demand/api/v1')
          .set('x-userid', invalidAuth.wrongTypeUserId)
          .set('x-authorization', validAuth.demand.authorization)
          .set('Content-Type', 'application/json')
          .send(v1TestData.valid);

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效authorization类型的请求', async () => {
        const response = await request
          .post('/demand/api/v1')
          .set('x-userid', validAuth.demand.userId.toString())
          .set('x-authorization', invalidAuth.wrongTypeAuth.toString())
          .set('Content-Type', 'application/json')
          .send(v1TestData.valid);

        expectParamsInvalidResponse(response);
      });
    });
  });

  describe('/demand/api/v2', () => {
    const v2TestData = generateV2TestData();

    describe('POST requests', () => {
      it('应该成功处理基本的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.valid.basic,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该成功处理带dimensions的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.valid.withDimensions,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该成功处理带timezone的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.valid.withTimezone,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少start_date的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.invalid.missingStartDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝缺少end_date的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.invalid.missingEndDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.invalid.invalidDateFormat,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效dimensions的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.invalid.invalidDimensions,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效timezone的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: v2TestData.invalid.invalidTimezone,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('GET requests', () => {
      it('应该成功处理有效的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/demand/api/v2',
            query: {
              ...v2TestData.valid.withTimezone,
              'dimensions[]': v2TestData.valid.withTimezone.dimensions
            }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该成功处理带多个dimensions的GET请求', async () => {
        const testData = v2TestData.valid.withTimezone;
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/demand/api/v2',
            query: {
              start_date: testData.start_date,
              end_date: testData.end_date,
              timezone: testData.timezone,
              'dimensions[]': testData.dimensions
            }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });
    });

    describe('参数验证测试', () => {
      it('应该验证日期范围不超过15天', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/demand/api/v2',
            body: {
              start_date: '2025-05-01',
              end_date: '2025-05-20' // 超过15天
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该接受有效的时区参数', async () => {
        for (const timezone of validTimezones.slice(0, 2)) {
          // 测试前两个时区
          const response = await createAuthenticatedRequest(
            request,
            {
              method: 'POST',
              url: '/demand/api/v2',
              body: {
                ...v2TestData.valid.basic,
                timezone
              },
              headers: { 'Content-Type': 'application/json' }
            },
            'demand'
          );

          expectReportDataResponse(response);
        }
      });
    });
  });
});
