/**
 * E2E测试数据生成器
 * 提供各种测试场景所需的测试数据
 */

import moment from 'moment';

/**
 * 有效的测试认证数据
 */
export const validAuth = {
  ssp: {
    userId: 38155,
    authorization: '4737b03b8e4b596818d7e4e2041bfaee'
  },
  demand: {
    userId: 32375,
    authorization: '82de8e04e0e994c969fa2b9be48f93a4'
  },
  report: {
    authorization: '9ee9fcab5942233541411e9008c005bf'
  }
};

/**
 * 无效的测试认证数据
 */
export const invalidAuth = {
  invalidUserId: 'invalid_user_id',
  invalidAuth: 'invalid_authorization',
  emptyUserId: '',
  emptyAuth: '',
  wrongTypeUserId: '12345', // 应该是数字但传字符串
  wrongTypeAuth: 12345 // 应该是字符串但传数字
};

/**
 * 生成有效的日期范围
 */
export function generateValidDateRange(daysBack: number = 7) {
  const endDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
  const startDate = moment().subtract(daysBack, 'days').format('YYYY-MM-DD');
  return { startDate, endDate };
}

/**
 * 生成无效的日期数据
 */
export const invalidDates = {
  invalidFormat: {
    startDate: '2025-13-01', // 无效月份
    endDate: '2025-02-30'    // 无效日期
  },
  wrongFormat: {
    startDate: '25-05-20',   // 错误格式
    endDate: '25/05/24'      // 错误格式
  },
  futureDate: {
    startDate: moment().add(1, 'day').format('YYYY-MM-DD'),
    endDate: moment().add(7, 'days').format('YYYY-MM-DD')
  },
  rangeTooLarge: {
    startDate: moment().subtract(20, 'days').format('YYYY-MM-DD'),
    endDate: moment().subtract(1, 'day').format('YYYY-MM-DD')
  }
};

/**
 * 有效的维度参数
 */
export const validDimensions = {
  single: ['day'],
  multiple: ['day', 'region'],
  empty: []
};

/**
 * 无效的维度参数
 */
export const invalidDimensions = {
  invalidValue: ['invalid_dimension'],
  mixedValid: ['day', 'invalid_dimension'],
  wrongType: 'day' // 应该是数组但传字符串
};

/**
 * 有效的时区参数
 */
export const validTimezones = [
  'UTC+0',
  'UTC+8',
  'UTC-5',
  'America/New_York',
  'Asia/Shanghai'
];

/**
 * 无效的时区参数
 */
export const invalidTimezones = [
  'Invalid/Timezone',
  'UTC+25',
  'UTC-15',
  ''
];

/**
 * V1 API测试数据生成器
 */
export function generateV1TestData() {
  const validDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
  
  return {
    valid: {
      day: validDate
    },
    invalid: {
      missingDay: {},
      invalidDay: { day: '2025-13-01' },
      futureDay: { day: moment().add(1, 'day').format('YYYY-MM-DD') },
      wrongFormat: { day: '25-05-20' }
    }
  };
}

/**
 * V2 API测试数据生成器
 */
export function generateV2TestData() {
  const { startDate, endDate } = generateValidDateRange();
  
  return {
    valid: {
      basic: {
        start_date: startDate,
        end_date: endDate
      },
      withDimensions: {
        start_date: startDate,
        end_date: endDate,
        dimensions: ['day']
      },
      withTimezone: {
        start_date: startDate,
        end_date: endDate,
        dimensions: ['day', 'region'],
        timezone: 'UTC+0'
      }
    },
    invalid: {
      missingStartDate: {
        end_date: endDate
      },
      missingEndDate: {
        start_date: startDate
      },
      invalidDateFormat: {
        start_date: '25-05-20',
        end_date: '25-05-24'
      },
      invalidDimensions: {
        start_date: startDate,
        end_date: endDate,
        dimensions: ['invalid_dimension']
      },
      invalidTimezone: {
        start_date: startDate,
        end_date: endDate,
        timezone: 'Invalid/Timezone'
      }
    }
  };
}

/**
 * Report API测试数据生成器
 */
export function generateReportTestData() {
  const validDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
  
  return {
    valid: {
      singleDate: {
        date: [validDate]
      },
      multipleDates: {
        date: [
          moment().subtract(3, 'days').format('YYYY-MM-DD'),
          moment().subtract(2, 'days').format('YYYY-MM-DD'),
          validDate
        ]
      }
    },
    invalid: {
      missingDate: {},
      emptyDateArray: { date: [] },
      invalidDateFormat: { date: ['25-05-20'] },
      futureDate: { date: [moment().add(1, 'day').format('YYYY-MM-DD')] }
    }
  };
}

/**
 * Partner API测试数据生成器
 */
export function generatePartnerTestData() {
  const { startDate, endDate } = generateValidDateRange();
  
  return {
    demand: {
      valid: {
        start_date: startDate,
        end_date: endDate,
        dimensions: ['day', 'region'],
        timezone: 'UTC+0'
      }
    },
    supply: {
      valid: {
        start_date: startDate,
        end_date: endDate,
        dimensions: ['day', 'region'],
        timezone: 'UTC+0'
      }
    }
  };
}
