/**
 * E2E测试辅助函数
 * 提供通用的测试工具和断言函数
 */

import { expect } from 'vitest';
import supertest from 'supertest';
import { Server } from 'http';
import { validAuth } from './test-data';

/**
 * 标准API响应格式
 */
export interface ApiResponse {
  status: {
    code: number;
    message: string;
  };
  timestamp: string;
  data: any;
}

/**
 * 测试请求配置
 */
export interface TestRequestConfig {
  method: 'GET' | 'POST';
  url: string;
  headers?: Record<string, any>;
  query?: Record<string, any>;
  body?: Record<string, any>;
}

/**
 * 创建带认证头的请求
 */
export function createAuthenticatedRequest(
  request: supertest.Agent,
  config: TestRequestConfig,
  authType: 'ssp' | 'demand' | 'report' = 'ssp'
) {
  let req = request[config.method.toLowerCase() as 'get' | 'post'](config.url);

  // 添加认证头
  if (authType === 'report') {
    req = req.set('x-authorization', validAuth.report.authorization);
  } else {
    const auth = validAuth[authType];
    req = req
      .set('x-userid', auth.userId.toString())
      .set('x-authorization', auth.authorization);
  }

  // 添加其他头部
  if (config.headers) {
    Object.entries(config.headers).forEach(([key, value]) => {
      req = req.set(key, value);
    });
  }

  // 添加查询参数
  if (config.query) {
    req = req.query(config.query);
  }

  // 添加请求体
  if (config.body && config.method === 'POST') {
    req = req.send(config.body);
  }

  return req;
}

/**
 * 创建无认证的请求
 */
export function createUnauthenticatedRequest(
  request: supertest.Agent,
  config: TestRequestConfig
) {
  let req = request[config.method.toLowerCase() as 'get' | 'post'](config.url);

  // 添加其他头部
  if (config.headers) {
    Object.entries(config.headers).forEach(([key, value]) => {
      req = req.set(key, value);
    });
  }

  // 添加查询参数
  if (config.query) {
    req = req.query(config.query);
  }

  // 添加请求体
  if (config.body && config.method === 'POST') {
    req = req.send(config.body);
  }

  return req;
}

/**
 * 验证成功响应格式
 */
export function expectSuccessResponse(response: supertest.Response, expectedDataType?: string) {
  expect(response.status).toBe(200);
  expect(response.body).toHaveProperty('status');
  expect(response.body).toHaveProperty('timestamp');
  expect(response.body).toHaveProperty('data');
  
  expect(response.body.status).toHaveProperty('code', 0);
  expect(response.body.status).toHaveProperty('message', 'success');
  
  if (expectedDataType) {
    expect(typeof response.body.data).toBe(expectedDataType);
  }
}

/**
 * 验证错误响应格式
 */
export function expectErrorResponse(
  response: supertest.Response, 
  expectedStatus: number,
  expectedCode?: number,
  expectedMessage?: string
) {
  expect(response.status).toBe(expectedStatus);
  expect(response.body).toHaveProperty('status');
  expect(response.body).toHaveProperty('timestamp');
  
  if (expectedCode !== undefined) {
    expect(response.body.status.code).toBe(expectedCode);
  }
  
  if (expectedMessage) {
    expect(response.body.status.message).toContain(expectedMessage);
  }
}

/**
 * 验证参数无效响应
 */
export function expectParamsInvalidResponse(response: supertest.Response) {
  expectErrorResponse(response, 200, 1001); // PARAMS_INVALID code
}

/**
 * 验证认证失败响应
 */
export function expectAuthFailureResponse(response: supertest.Response) {
  expectErrorResponse(response, 403); // TOKEN_INVALID or similar
}

/**
 * 验证访问限制响应
 */
export function expectRateLimitResponse(response: supertest.Response) {
  expectErrorResponse(response, 200, 1006); // REQUEST_COUNT_LIMIT code
}

/**
 * 验证报表数据响应格式
 */
export function expectReportDataResponse(response: supertest.Response) {
  expectSuccessResponse(response, 'object');
  expect(response.body.data).toHaveProperty('total');
  expect(response.body.data).toHaveProperty('data');
  expect(Array.isArray(response.body.data.data)).toBe(true);
  expect(typeof response.body.data.total).toBe('number');
}

/**
 * 验证下载链接响应格式
 */
export function expectDownloadUrlResponse(response: supertest.Response) {
  expectSuccessResponse(response, 'object');
  expect(Array.isArray(response.body.data)).toBe(true);
  
  if (response.body.data.length > 0) {
    const item = response.body.data[0];
    expect(item).toHaveProperty('path');
    expect(item).toHaveProperty('date');
    expect(item).toHaveProperty('expire_time');
  }
}

/**
 * 等待指定时间
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number = 10): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 清理测试环境
 */
export async function cleanupTestEnvironment(server?: Server) {
  if (server) {
    return new Promise<void>((resolve) => {
      server.close(() => {
        resolve();
      });
    });
  }
}
