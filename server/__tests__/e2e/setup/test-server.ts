/**
 * E2E测试服务器启动配置
 * 用于在测试环境中启动Koa应用服务器
 */

import Koa from 'koa';
import koaBody from 'koa-body';
import compress from 'koa-compress';
import path from 'path';
import zlib from 'zlib';
import moduleAlias from 'module-alias';

// 文件路径别名需要放置前面
moduleAlias.addAlias('@', path.join(__dirname, '../../../src/'));

import routers from '../../../src/routers';

/**
 * 创建测试用的Koa应用实例
 * @returns Koa应用实例
 */
export function createTestApp(): Koa {
  const app = new Koa();

  app.use(
    koaBody({
      formLimit: '10mb',
      jsonLimit: '10mb',
      textLimit: '10mb',
      multipart: true
    })
  );

  // compress
  app.use(
    compress({
      threshold: 2048, // 2kb
      gzip: {
        flush: zlib.constants.Z_SYNC_FLUSH
      },
      deflate: {
        flush: zlib.constants.Z_SYNC_FLUSH
      },
      br: false // disable brotli
    })
  );

  // 简化的日志中间件用于测试
  app.use(async (ctx, next) => {
    const start = Date.now();
    await next();
    const responseTime = Date.now() - start;

    // 在测试环境中可以选择性地记录日志
    if (process.env.TEST_VERBOSE) {
      console.log(
        `${ctx.method} ${ctx.url} - ${ctx.status} - ${responseTime}ms`
      );
    }
  });

  // 初始化路由中间件
  app.use(routers.routes());

  return app;
}

/**
 * 启动测试服务器
 * @param port 端口号，默认为0（随机端口）
 * @returns Promise<Server>
 */
export function startTestServer(port: number = 0) {
  // 确保在导入其他模块之前设置环境变量
  process.env.TZ = 'UTC';
  process.env.NODE_ENV = 'dev';
  const app = createTestApp();
  return new Promise((resolve, reject) => {
    const server = app.listen(port, (err?: Error) => {
      if (err) {
        reject(err);
      } else {
        resolve(server);
      }
    });
  });
}
