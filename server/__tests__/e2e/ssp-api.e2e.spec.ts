/**
 * SSP API E2E 测试
 * 测试 /ssp/api/v1 和 /ssp/api/v2 接口
 */

import { Server } from 'http';
import supertest from 'supertest';
import { afterAll, beforeAll, describe, it } from 'vitest';
import {
  generateV1TestData,
  generateV2TestData,
  invalidAuth,
  validAuth
} from './setup/test-data';
import {
  cleanupTestEnvironment,
  createAuthenticatedRequest,
  createUnauthenticatedRequest,
  expectAuthFailureResponse,
  expectParamsInvalidResponse,
  expectReportDataResponse
} from './setup/test-helpers';
import { createTestApp } from './setup/test-server';

describe('SSP API E2E Tests', () => {
  let server: Server;
  let request: supertest.Agent;

  beforeAll(async () => {
    const app = createTestApp();
    server = app.listen();
    request = supertest(server);
  });

  afterAll(async () => {
    await cleanupTestEnvironment(server);
  });

  describe('/ssp/api/v1', () => {
    const v1TestData = generateV1TestData();

    describe('POST requests', () => {
      it('应该成功处理有效的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v1',
            body: v1TestData.valid,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少day参数的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v1',
            body: v1TestData.invalid.missingDay,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v1',
            body: v1TestData.invalid.invalidDay,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝未来日期的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v1',
            body: v1TestData.invalid.futureDay,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('GET requests', () => {
      it('应该成功处理有效的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/ssp/api/v1',
            query: v1TestData.valid
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少day参数的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/ssp/api/v1',
            query: v1TestData.invalid.missingDay
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('认证测试', () => {
      it('应该拒绝缺少认证头的请求', async () => {
        const response = await createUnauthenticatedRequest(request, {
          method: 'POST',
          url: '/ssp/api/v1',
          body: v1TestData.valid,
          headers: { 'Content-Type': 'application/json' }
        });

        expectAuthFailureResponse(response);
      });

      it('应该拒绝无效userid类型的请求', async () => {
        const response = await request
          .post('/ssp/api/v1')
          .set('x-userid', invalidAuth.wrongTypeUserId)
          .set('x-authorization', validAuth.ssp.authorization)
          .set('Content-Type', 'application/json')
          .send(v1TestData.valid);

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效authorization类型的请求', async () => {
        const response = await request
          .post('/ssp/api/v1')
          .set('x-userid', validAuth.ssp.userId.toString())
          .set('x-authorization', invalidAuth.wrongTypeAuth.toString())
          .set('Content-Type', 'application/json')
          .send(v1TestData.valid);

        expectParamsInvalidResponse(response);
      });
    });
  });

  describe('/ssp/api/v2', () => {
    const v2TestData = generateV2TestData();

    describe('POST requests', () => {
      it('应该成功处理基本的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.valid.basic,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该成功处理带dimensions的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.valid.withDimensions,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该成功处理带timezone的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.valid.withTimezone,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少start_date的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.invalid.missingStartDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝缺少end_date的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.invalid.missingEndDate,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.invalid.invalidDateFormat,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效dimensions的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.invalid.invalidDimensions,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效timezone的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/ssp/api/v2',
            body: v2TestData.invalid.invalidTimezone,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('GET requests', () => {
      it('应该成功处理有效的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/ssp/api/v2',
            query: {
              ...v2TestData.valid.withDimensions,
              'dimensions[]': v2TestData.valid.withDimensions.dimensions
            }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });
    });
  });
});
