/**
 * Partner API E2E 测试
 * 测试 /partner/api/demand 和 /partner/api/supply 接口
 */

import { Server } from 'http';
import supertest from 'supertest';
import { afterAll, beforeAll, describe, it } from 'vitest';
import {
  generatePartnerTestData,
  invalidAuth,
  validAuth,
  validTimezones
} from './setup/test-data';
import {
  cleanupTestEnvironment,
  createAuthenticatedRequest,
  createUnauthenticatedRequest,
  expectAuthFailureResponse,
  expectParamsInvalidResponse,
  expectReportDataResponse
} from './setup/test-helpers';
import { createTestApp } from './setup/test-server';

describe('Partner API E2E Tests', () => {
  let server: Server;
  let request: supertest.Agent;

  beforeAll(async () => {
    const app = createTestApp();
    server = app.listen();
    request = supertest(server);
  });

  afterAll(async () => {
    await cleanupTestEnvironment(server);
  });

  describe('/partner/api/demand', () => {
    const partnerTestData = generatePartnerTestData();

    describe('POST requests', () => {
      it('应该成功处理有效的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/demand',
            body: partnerTestData.demand.valid,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少start_date的请求', async () => {
        const { start_date, ...invalidData } = partnerTestData.demand.valid;
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/demand',
            body: invalidData,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝缺少end_date的请求', async () => {
        const { end_date, ...invalidData } = partnerTestData.demand.valid;
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/demand',
            body: invalidData,
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/demand',
            body: {
              ...partnerTestData.demand.valid,
              start_date: '25-05-20',
              end_date: '25-05-24'
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效dimensions的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/demand',
            body: {
              ...partnerTestData.demand.valid,
              dimensions: ['invalid_dimension']
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效timezone的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/demand',
            body: {
              ...partnerTestData.demand.valid,
              timezone: 'Invalid/Timezone'
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'demand'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('GET requests', () => {
      it('应该成功处理有效的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/partner/api/demand',
            query: {
              ...partnerTestData.demand.valid,
              'dimensions[]': partnerTestData.demand.valid.dimensions
            }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });

      it('应该处理单个dimension的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/partner/api/demand',
            query: {
              ...partnerTestData.demand.valid,
              'dimensions[]': ['day']
            }
          },
          'demand'
        );

        expectReportDataResponse(response);
      });
    });

    describe('认证测试', () => {
      it('应该拒绝缺少认证头的请求', async () => {
        const response = await createUnauthenticatedRequest(request, {
          method: 'POST',
          url: '/partner/api/demand',
          body: partnerTestData.demand.valid,
          headers: { 'Content-Type': 'application/json' }
        });

        expectAuthFailureResponse(response);
      });

      it('应该拒绝无效userid类型的请求', async () => {
        const response = await request
          .post('/partner/api/demand')
          .set('x-userid', invalidAuth.wrongTypeUserId)
          .set('x-authorization', validAuth.demand.authorization)
          .set('Content-Type', 'application/json')
          .send(partnerTestData.demand.valid);

        expectParamsInvalidResponse(response);
      });
    });
  });

  describe('/partner/api/supply', () => {
    const partnerTestData = generatePartnerTestData();

    describe('POST requests', () => {
      it('应该成功处理有效的POST请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: partnerTestData.supply.valid,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该拒绝缺少start_date的请求', async () => {
        const { start_date, ...invalidData } = partnerTestData.supply.valid;
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: invalidData,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝缺少end_date的请求', async () => {
        const { end_date, ...invalidData } = partnerTestData.supply.valid;
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: invalidData,
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效日期格式的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: {
              ...partnerTestData.supply.valid,
              start_date: '25-05-20',
              end_date: '25-05-24'
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效dimensions的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: {
              ...partnerTestData.supply.valid,
              dimensions: ['invalid_dimension']
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该拒绝无效timezone的请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: {
              ...partnerTestData.supply.valid,
              timezone: 'Invalid/Timezone'
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });
    });

    describe('GET requests', () => {
      it('应该成功处理有效的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/partner/api/supply',
            query: {
              ...partnerTestData.supply.valid,
              'dimensions[]': partnerTestData.supply.valid.dimensions
            }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });

      it('应该处理单个dimension的GET请求', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'GET',
            url: '/partner/api/supply',
            query: {
              ...partnerTestData.supply.valid,
              'dimensions[]': ['day']
            }
          },
          'ssp'
        );

        expectReportDataResponse(response);
      });
    });

    describe('认证测试', () => {
      it('应该拒绝缺少认证头的请求', async () => {
        const response = await createUnauthenticatedRequest(request, {
          method: 'POST',
          url: '/partner/api/supply',
          body: partnerTestData.supply.valid,
          headers: { 'Content-Type': 'application/json' }
        });

        expectAuthFailureResponse(response);
      });

      it('应该拒绝无效userid类型的请求', async () => {
        const response = await request
          .post('/partner/api/supply')
          .set('x-userid', invalidAuth.wrongTypeUserId)
          .set('x-authorization', validAuth.ssp.authorization)
          .set('Content-Type', 'application/json')
          .send(partnerTestData.supply.valid);

        expectParamsInvalidResponse(response);
      });
    });

    describe('参数验证测试', () => {
      it('应该验证日期范围不超过15天', async () => {
        const response = await createAuthenticatedRequest(
          request,
          {
            method: 'POST',
            url: '/partner/api/supply',
            body: {
              start_date: '2025-05-01',
              end_date: '2025-05-20', // 超过15天
              dimensions: ['day']
            },
            headers: { 'Content-Type': 'application/json' }
          },
          'ssp'
        );

        expectParamsInvalidResponse(response);
      });

      it('应该接受有效的时区参数', async () => {
        for (const timezone of validTimezones.slice(0, 2)) {
          // 测试前两个时区
          const response = await createAuthenticatedRequest(
            request,
            {
              method: 'POST',
              url: '/partner/api/supply',
              body: {
                ...partnerTestData.supply.valid,
                timezone
              },
              headers: { 'Content-Type': 'application/json' }
            },
            'ssp'
          );

          expectReportDataResponse(response);
        }
      });
    });
  });
});
